import { createHash } from 'crypto'
import { z } from 'zod'
import { PUBLIC_GROUP } from '~/server/utils/backend/activitypub/activities'
import { addObjectInOutbox } from '~/server/utils/backend/activitypub/actors/outbox'
import { getActorById } from '~/server/utils/backend/activitypub/actors'
import { createNote, type Note } from '~/server/utils/backend/activitypub/objects/note'
import { toMastodonStatusFromObject } from '~/server/utils/backend/mastodon/status'
import { uploadToR2 } from '~/server/utils/backend/storage/r2-upload'

// Allowed document file types
const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/csv',
  'application/rtf',
  'application/vnd.oasis.opendocument.text',
  'application/vnd.oasis.opendocument.spreadsheet',
  'application/vnd.oasis.opendocument.presentation',
  'application/zip',
  'application/x-zip-compressed',
  'application/epub+zip',
]

// Maximum file size: 50MB
const MAX_FILE_SIZE = 50 * 1024 * 1024

const schema = z.object({
  file: z.instanceof(File),
})

export default defineEventHandler(async (event) => {
  const actor = await requireUserSession(event)

  // Parse form data
  const { file } = await readValidatedFormData(event, schema.parse)

  // Use original filename as title
  const title = file.name

  // Validate file type
  if (!ALLOWED_DOCUMENT_TYPES.includes(file.type)) {
    throw createError({
      statusCode: 400,
      statusMessage: `File type ${file.type} is not allowed. Allowed types: ${ALLOWED_DOCUMENT_TYPES.join(', ')}`,
    })
  }

  // Validate file size
  if (file.size > MAX_FILE_SIZE) {
    throw createError({
      statusCode: 400,
      statusMessage: `File size ${file.size} exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes`,
    })
  }

  // Generate unique filename
  const fileExtension = file.name.split('.').pop() || ''
  const uniqueId = crypto.randomUUID()
  const filename = `documents/${uniqueId}.${fileExtension}`

  // Calculate file hash for deduplication
  const fileBuffer = await file.arrayBuffer()
  const hash = createHash('sha256')
  hash.update(new Uint8Array(fileBuffer))
  const fileHash = hash.digest('hex')

  // Check if file with same hash already exists for this user
  const existingDoc = await useEnv().DB.prepare(`
    SELECT id, properties FROM objects
    WHERE original_actor_id = ? AND type = 'Note' AND json_extract(properties, '$.file_hash') = ?
  `).bind(actor.id, fileHash).first()

  if (existingDoc) {
    const props = JSON.parse(existingDoc.properties as string)
    throw createError({
      statusCode: 409,
      statusMessage: `A document with identical content already exists: "${props.title}"`,
    })
  }

  try {
    // Upload to R2 using multipart upload for better performance and reliability
    await uploadToR2(useEnv().BUCKET, filename, file)

    // Get actor for creating the note object
    const actorObj = await getActorById(useEnv().DB, actor.id)
    if (!actorObj) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Actor not found',
      })
    }

    const note = await createNote(
      getRequestDomain(event),
      useEnv().DB,
      '',
      actorObj,
      [PUBLIC_GROUP],
      [actorObj.followers?.toString()],
      [], // no attachments
      {
        title,
        filename,
        original_filename: file.name,
        file_type: file.type,
        file_size: file.size,
        file_hash: fileHash,
      },
    )

    // Add to outbox for public visibility
    await addObjectInOutbox(useEnv().DB, actorObj.id.toString(), note.id.toString())

    // Convert to Mastodon Status format
    const status = await toMastodonStatusFromObject(useEnv().DB, note as Note, getRequestDomain(event))

    return status
  }
  catch (error) {
    console.error('Document upload failed:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to upload document',
    })
  }
})
